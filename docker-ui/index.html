<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CloudDrive - Docker Management Interface</title>
    <link rel="stylesheet" href="styles/glass-morphism.css">
    <link rel="stylesheet" href="styles/macos-theme.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Background with dynamic gradient -->
    <div class="background-container">
        <div class="gradient-bg"></div>
        <div class="liquid-overlay"></div>
    </div>

    <!-- Main Application Container -->
    <div class="app-container">
        <!-- Top Navigation Bar -->
        <nav class="top-nav glass-panel">
            <div class="nav-left">
                <div class="app-logo">
                    <i class="fab fa-docker"></i>
                    <span>CloudDrive</span>
                </div>
                <div class="nav-tabs">
                    <button class="nav-tab active" data-view="containers">
                        <i class="fas fa-cube"></i>
                        <span>Containers</span>
                    </button>
                    <button class="nav-tab" data-view="images">
                        <i class="fas fa-layer-group"></i>
                        <span>Images</span>
                    </button>
                    <button class="nav-tab" data-view="volumes">
                        <i class="fas fa-database"></i>
                        <span>Volumes</span>
                    </button>
                    <button class="nav-tab" data-view="networks">
                        <i class="fas fa-network-wired"></i>
                        <span>Networks</span>
                    </button>
                </div>
            </div>
            <div class="nav-right">
                <button class="action-btn glass-btn" id="refresh-btn">
                    <i class="fas fa-sync-alt"></i>
                </button>
                <button class="action-btn glass-btn" id="settings-btn">
                    <i class="fas fa-cog"></i>
                </button>
                <div class="user-profile">
                    <img src="https://via.placeholder.com/32x32/007AFF/FFFFFF?text=U" alt="User" class="profile-avatar">
                </div>
            </div>
        </nav>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Sidebar -->
            <aside class="sidebar glass-panel">
                <div class="sidebar-section">
                    <h3>Quick Actions</h3>
                    <button class="sidebar-btn glass-btn">
                        <i class="fas fa-plus"></i>
                        <span>New Container</span>
                    </button>
                    <button class="sidebar-btn glass-btn">
                        <i class="fas fa-download"></i>
                        <span>Pull Image</span>
                    </button>
                    <button class="sidebar-btn glass-btn">
                        <i class="fas fa-upload"></i>
                        <span>Build Image</span>
                    </button>
                </div>

                <div class="sidebar-section">
                    <h3>System Info</h3>
                    <div class="system-stats glass-card">
                        <div class="stat-item">
                            <span class="stat-label">CPU Usage</span>
                            <div class="stat-bar">
                                <div class="stat-fill" style="width: 45%"></div>
                            </div>
                            <span class="stat-value">45%</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Memory</span>
                            <div class="stat-bar">
                                <div class="stat-fill" style="width: 62%"></div>
                            </div>
                            <span class="stat-value">62%</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Storage</span>
                            <div class="stat-bar">
                                <div class="stat-fill" style="width: 78%"></div>
                            </div>
                            <span class="stat-value">78%</span>
                        </div>
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3>Recent Activity</h3>
                    <div class="activity-list">
                        <div class="activity-item">
                            <i class="fas fa-play text-success"></i>
                            <span>nginx started</span>
                            <time>2m ago</time>
                        </div>
                        <div class="activity-item">
                            <i class="fas fa-stop text-warning"></i>
                            <span>redis stopped</span>
                            <time>5m ago</time>
                        </div>
                        <div class="activity-item">
                            <i class="fas fa-download text-info"></i>
                            <span>postgres pulled</span>
                            <time>10m ago</time>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- Content Panel -->
            <section class="content-panel">
                <!-- Containers View -->
                <div class="view-container active" id="containers-view">
                    <div class="view-header glass-panel">
                        <h2>Docker Containers</h2>
                        <div class="view-actions">
                            <div class="search-box glass-input">
                                <i class="fas fa-search"></i>
                                <input type="text" placeholder="Search containers...">
                            </div>
                            <button class="action-btn glass-btn primary">
                                <i class="fas fa-plus"></i>
                                <span>Create Container</span>
                            </button>
                        </div>
                    </div>

                    <div class="containers-grid">
                        <!-- Container Card Template -->
                        <div class="container-card glass-card">
                            <div class="card-header">
                                <div class="container-status running">
                                    <i class="fas fa-circle"></i>
                                    <span>Running</span>
                                </div>
                                <div class="card-actions">
                                    <button class="icon-btn" title="Start/Stop">
                                        <i class="fas fa-power-off"></i>
                                    </button>
                                    <button class="icon-btn" title="Restart">
                                        <i class="fas fa-redo"></i>
                                    </button>
                                    <button class="icon-btn" title="More">
                                        <i class="fas fa-ellipsis-h"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <h3>nginx-proxy</h3>
                                <p class="container-image">nginx:latest</p>
                                <div class="container-ports">
                                    <span class="port-badge">80:80</span>
                                    <span class="port-badge">443:443</span>
                                </div>
                            </div>
                            <div class="card-footer">
                                <div class="resource-usage">
                                    <div class="usage-item">
                                        <i class="fas fa-microchip"></i>
                                        <span>12%</span>
                                    </div>
                                    <div class="usage-item">
                                        <i class="fas fa-memory"></i>
                                        <span>256MB</span>
                                    </div>
                                </div>
                                <time class="created-time">Created 2 days ago</time>
                            </div>
                        </div>

                        <!-- More container cards would be dynamically generated -->
                        <div class="container-card glass-card">
                            <div class="card-header">
                                <div class="container-status stopped">
                                    <i class="fas fa-circle"></i>
                                    <span>Stopped</span>
                                </div>
                                <div class="card-actions">
                                    <button class="icon-btn" title="Start/Stop">
                                        <i class="fas fa-play"></i>
                                    </button>
                                    <button class="icon-btn" title="Restart">
                                        <i class="fas fa-redo"></i>
                                    </button>
                                    <button class="icon-btn" title="More">
                                        <i class="fas fa-ellipsis-h"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <h3>redis-cache</h3>
                                <p class="container-image">redis:alpine</p>
                                <div class="container-ports">
                                    <span class="port-badge">6379:6379</span>
                                </div>
                            </div>
                            <div class="card-footer">
                                <div class="resource-usage">
                                    <div class="usage-item">
                                        <i class="fas fa-microchip"></i>
                                        <span>0%</span>
                                    </div>
                                    <div class="usage-item">
                                        <i class="fas fa-memory"></i>
                                        <span>0MB</span>
                                    </div>
                                </div>
                                <time class="created-time">Created 1 week ago</time>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Other views (Images, Volumes, Networks) would be similar structures -->
                <div class="view-container" id="images-view">
                    <div class="view-header glass-panel">
                        <h2>Docker Images</h2>
                        <div class="view-actions">
                            <div class="search-box glass-input">
                                <i class="fas fa-search"></i>
                                <input type="text" placeholder="Search images...">
                            </div>
                            <button class="action-btn glass-btn primary">
                                <i class="fas fa-download"></i>
                                <span>Pull Image</span>
                            </button>
                        </div>
                    </div>
                    <div class="content-placeholder">
                        <i class="fas fa-layer-group"></i>
                        <p>Images view content will be loaded here</p>
                    </div>
                </div>

                <div class="view-container" id="volumes-view">
                    <div class="view-header glass-panel">
                        <h2>Docker Volumes</h2>
                        <div class="view-actions">
                            <div class="search-box glass-input">
                                <i class="fas fa-search"></i>
                                <input type="text" placeholder="Search volumes...">
                            </div>
                            <button class="action-btn glass-btn primary">
                                <i class="fas fa-plus"></i>
                                <span>Create Volume</span>
                            </button>
                        </div>
                    </div>
                    <div class="content-placeholder">
                        <i class="fas fa-database"></i>
                        <p>Volumes view content will be loaded here</p>
                    </div>
                </div>

                <div class="view-container" id="networks-view">
                    <div class="view-header glass-panel">
                        <h2>Docker Networks</h2>
                        <div class="view-actions">
                            <div class="search-box glass-input">
                                <i class="fas fa-search"></i>
                                <input type="text" placeholder="Search networks...">
                            </div>
                            <button class="action-btn glass-btn primary">
                                <i class="fas fa-plus"></i>
                                <span>Create Network</span>
                            </button>
                        </div>
                    </div>
                    <div class="content-placeholder">
                        <i class="fas fa-network-wired"></i>
                        <p>Networks view content will be loaded here</p>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Modal Overlay -->
    <div class="modal-overlay" id="modal-overlay">
        <div class="modal glass-panel">
            <div class="modal-header">
                <h3 id="modal-title">Modal Title</h3>
                <button class="modal-close" id="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content" id="modal-content">
                <!-- Modal content will be dynamically loaded -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/liquid-effects.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
