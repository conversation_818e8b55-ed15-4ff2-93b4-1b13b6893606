<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CloudDrive Docker UI - Demo</title>
    <link rel="stylesheet" href="styles/glass-morphism.css">
    <link rel="stylesheet" href="styles/macos-theme.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .demo-header {
            text-align: center;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .demo-title {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }
        
        .demo-subtitle {
            font-size: 1.2rem;
            color: var(--macos-text-secondary);
            margin-bottom: 2rem;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .feature-card {
            padding: 2rem;
            text-align: center;
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--macos-blue) 0%, var(--macos-purple) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .feature-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--macos-text-primary);
        }
        
        .feature-description {
            color: var(--macos-text-secondary);
            line-height: 1.6;
        }
        
        .demo-actions {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin: 2rem 0;
            flex-wrap: wrap;
        }
        
        .demo-btn {
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 600;
            text-decoration: none;
            border-radius: 12px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .demo-btn.primary {
            background: linear-gradient(135deg, var(--macos-blue) 0%, var(--macos-purple) 100%);
            color: white;
        }
        
        .demo-btn.secondary {
            background: rgba(255, 255, 255, 0.1);
            color: var(--macos-text-primary);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        
        .tech-stack {
            margin: 3rem 0;
            text-align: center;
        }
        
        .tech-stack h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--macos-text-primary);
        }
        
        .tech-badges {
            display: flex;
            justify-content: center;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .tech-badge {
            padding: 0.5rem 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            font-size: 0.9rem;
            color: var(--macos-text-secondary);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .preview-container {
            margin: 3rem 0;
            text-align: center;
        }
        
        .preview-image {
            max-width: 100%;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        @media (max-width: 768px) {
            .demo-title {
                font-size: 2rem;
            }
            
            .demo-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .demo-btn {
                width: 100%;
                max-width: 300px;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Background with dynamic gradient -->
    <div class="background-container">
        <div class="gradient-bg"></div>
        <div class="liquid-overlay"></div>
    </div>

    <!-- Demo Content -->
    <div class="app-container">
        <!-- Demo Header -->
        <header class="demo-header">
            <h1 class="demo-title">CloudDrive Docker UI</h1>
            <p class="demo-subtitle">
                A modern, macOS-inspired Docker management interface with glass morphism effects and liquid animations
            </p>
            
            <div class="demo-actions">
                <a href="index.html" class="demo-btn primary glass-btn">
                    <i class="fas fa-rocket"></i>
                    Launch Application
                </a>
                <a href="#features" class="demo-btn secondary glass-btn">
                    <i class="fas fa-info-circle"></i>
                    Learn More
                </a>
            </div>
        </header>

        <!-- Features Section -->
        <section id="features" class="glass-panel" style="padding: 3rem; margin: 2rem 0;">
            <h2 style="text-align: center; font-size: 2rem; margin-bottom: 2rem; color: var(--macos-text-primary);">
                Key Features
            </h2>
            
            <div class="feature-grid">
                <div class="feature-card glass-card">
                    <div class="feature-icon">
                        <i class="fas fa-magic"></i>
                    </div>
                    <h3 class="feature-title">Glass Morphism Design</h3>
                    <p class="feature-description">
                        Beautiful translucent materials with rich tactile details, featuring app icon-style glossy finish with subtle reflections and highlights.
                    </p>
                </div>
                
                <div class="feature-card glass-card">
                    <div class="feature-icon">
                        <i class="fas fa-water"></i>
                    </div>
                    <h3 class="feature-title">Liquid Glass Effects</h3>
                    <p class="feature-description">
                        Dynamic blur, semi-transparency, and color diffusion create "liquid in motion" visual effects during user interactions.
                    </p>
                </div>
                
                <div class="feature-card glass-card">
                    <div class="feature-icon">
                        <i class="fab fa-apple"></i>
                    </div>
                    <h3 class="feature-title">macOS-Inspired</h3>
                    <p class="feature-description">
                        Logical and intuitive UI layout following macOS design principles with smooth, fluid animations throughout the interface.
                    </p>
                </div>
                
                <div class="feature-card glass-card">
                    <div class="feature-icon">
                        <i class="fas fa-cube"></i>
                    </div>
                    <h3 class="feature-title">Docker Management</h3>
                    <p class="feature-description">
                        Complete Docker container, image, volume, and network management with real-time status updates and resource monitoring.
                    </p>
                </div>
                
                <div class="feature-card glass-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 class="feature-title">Responsive Design</h3>
                    <p class="feature-description">
                        Optimized for all screen sizes with adaptive layouts and touch-friendly interactions for mobile devices.
                    </p>
                </div>
                
                <div class="feature-card glass-card">
                    <div class="feature-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h3 class="feature-title">Performance Optimized</h3>
                    <p class="feature-description">
                        Smooth animations with hardware acceleration and performance optimizations for low-end devices.
                    </p>
                </div>
            </div>
        </section>

        <!-- Technology Stack -->
        <section class="tech-stack glass-panel" style="padding: 2rem; margin: 2rem 0;">
            <h3>Built With Modern Technologies</h3>
            <div class="tech-badges">
                <span class="tech-badge">HTML5</span>
                <span class="tech-badge">CSS3 Glass Morphism</span>
                <span class="tech-badge">JavaScript ES6+</span>
                <span class="tech-badge">CSS Grid & Flexbox</span>
                <span class="tech-badge">CSS Backdrop Filter</span>
                <span class="tech-badge">CSS Animations</span>
                <span class="tech-badge">Responsive Design</span>
                <span class="tech-badge">Font Awesome Icons</span>
                <span class="tech-badge">SF Pro Display Font</span>
            </div>
        </section>

        <!-- Design Principles -->
        <section class="glass-panel" style="padding: 3rem; margin: 2rem 0;">
            <h2 style="text-align: center; font-size: 2rem; margin-bottom: 2rem; color: var(--macos-text-primary);">
                Design Principles
            </h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h4 style="color: var(--macos-blue); margin-bottom: 1rem;">Gaussian Blur Effects</h4>
                    <p style="color: var(--macos-text-secondary);">
                        Implementing depth and elegance through carefully crafted blur effects that create visual hierarchy and focus.
                    </p>
                </div>
                
                <div class="feature-card">
                    <h4 style="color: var(--macos-purple); margin-bottom: 1rem;">Smooth Animations</h4>
                    <p style="color: var(--macos-text-secondary);">
                        Fluid transitions and micro-interactions that provide premium-feeling user experience with natural motion curves.
                    </p>
                </div>
                
                <div class="feature-card">
                    <h4 style="color: var(--macos-green); margin-bottom: 1rem;">Intuitive Layout</h4>
                    <p style="color: var(--macos-text-secondary);">
                        Logical information architecture following macOS design patterns for familiar and efficient navigation.
                    </p>
                </div>
            </div>
        </section>

        <!-- Call to Action -->
        <section class="glass-panel" style="padding: 3rem; margin: 2rem 0; text-align: center;">
            <h2 style="font-size: 2rem; margin-bottom: 1rem; color: var(--macos-text-primary);">
                Ready to Experience the Future of Docker Management?
            </h2>
            <p style="font-size: 1.1rem; color: var(--macos-text-secondary); margin-bottom: 2rem;">
                Launch the application and explore the beautiful, functional interface designed for modern Docker workflows.
            </p>
            
            <div class="demo-actions">
                <a href="index.html" class="demo-btn primary liquid-btn">
                    <i class="fas fa-play"></i>
                    Launch Application
                </a>
                <a href="https://github.com/shuding/liquid-glass" class="demo-btn secondary glass-btn" target="_blank">
                    <i class="fab fa-github"></i>
                    View Source
                </a>
            </div>
        </section>

        <!-- Footer -->
        <footer style="text-align: center; padding: 2rem; color: var(--macos-text-tertiary);">
            <p>
                Inspired by CloudDrive2.com and iOS 16 liquid glass characteristics
            </p>
            <p style="margin-top: 0.5rem;">
                Built with ❤️ using modern web technologies
            </p>
        </footer>
    </div>

    <!-- Scripts -->
    <script src="js/liquid-effects.js"></script>
    <script>
        // Demo-specific enhancements
        document.addEventListener('DOMContentLoaded', () => {
            // Add extra liquid blobs for demo
            if (window.liquidEffects) {
                const overlay = document.querySelector('.liquid-overlay');
                if (overlay) {
                    // Add more colorful blobs for demo
                    const demoColors = [
                        'rgba(255, 149, 0, 0.3)',
                        'rgba(255, 45, 85, 0.3)',
                        'rgba(88, 86, 214, 0.3)'
                    ];
                    
                    demoColors.forEach((color, i) => {
                        const blob = document.createElement('div');
                        blob.className = 'liquid-blob';
                        blob.style.cssText = `
                            position: absolute;
                            width: ${150 + Math.random() * 100}px;
                            height: ${150 + Math.random() * 100}px;
                            background: ${color};
                            border-radius: 50%;
                            filter: blur(40px);
                            animation: liquidFloat ${15 + Math.random() * 10}s infinite ease-in-out;
                            animation-delay: ${i * 3}s;
                            top: ${Math.random() * 100}%;
                            left: ${Math.random() * 100}%;
                        `;
                        overlay.appendChild(blob);
                    });
                }
            }
            
            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
            
            // Add parallax effect to demo title
            window.addEventListener('scroll', () => {
                const scrolled = window.pageYOffset;
                const title = document.querySelector('.demo-title');
                if (title) {
                    title.style.transform = `translateY(${scrolled * 0.3}px)`;
                }
            });
        });
    </script>
</body>
</html>
