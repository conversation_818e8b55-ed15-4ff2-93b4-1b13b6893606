/**
 * Liquid Glass Effects - Dynamic visual effects for the Docker UI
 * Inspired by iOS 16 liquid glass characteristics
 */

class LiquidEffects {
    constructor() {
        this.liquidOverlay = null;
        this.blobs = [];
        this.mousePosition = { x: 0, y: 0 };
        this.isInitialized = false;
        
        this.init();
    }

    init() {
        if (this.isInitialized) return;
        
        this.createLiquidOverlay();
        this.createLiquidBlobs();
        this.setupMouseTracking();
        this.setupIntersectionObserver();
        this.startAnimationLoop();
        
        this.isInitialized = true;
    }

    createLiquidOverlay() {
        this.liquidOverlay = document.querySelector('.liquid-overlay');
        if (!this.liquidOverlay) {
            this.liquidOverlay = document.createElement('div');
            this.liquidOverlay.className = 'liquid-overlay';
            document.body.appendChild(this.liquidOverlay);
        }
    }

    createLiquidBlobs() {
        const colors = [
            'rgba(0, 122, 255, 0.3)',
            'rgba(88, 86, 214, 0.3)',
            'rgba(255, 45, 85, 0.3)',
            'rgba(52, 199, 89, 0.3)',
            'rgba(255, 204, 0, 0.3)'
        ];

        for (let i = 0; i < 5; i++) {
            const blob = document.createElement('div');
            blob.className = 'liquid-blob';
            
            const size = Math.random() * 200 + 100;
            blob.style.width = `${size}px`;
            blob.style.height = `${size}px`;
            blob.style.background = colors[i % colors.length];
            blob.style.left = `${Math.random() * 100}%`;
            blob.style.top = `${Math.random() * 100}%`;
            blob.style.animationDelay = `${Math.random() * 20}s`;
            
            this.liquidOverlay.appendChild(blob);
            this.blobs.push({
                element: blob,
                x: Math.random() * window.innerWidth,
                y: Math.random() * window.innerHeight,
                vx: (Math.random() - 0.5) * 2,
                vy: (Math.random() - 0.5) * 2,
                size: size
            });
        }
    }

    setupMouseTracking() {
        document.addEventListener('mousemove', (e) => {
            this.mousePosition.x = e.clientX;
            this.mousePosition.y = e.clientY;
            this.updateBlobsBasedOnMouse();
        });
    }

    updateBlobsBasedOnMouse() {
        this.blobs.forEach((blob, index) => {
            const dx = this.mousePosition.x - blob.x;
            const dy = this.mousePosition.y - blob.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance < 200) {
                const force = (200 - distance) / 200;
                blob.vx += (dx / distance) * force * 0.5;
                blob.vy += (dy / distance) * force * 0.5;
            }
        });
    }

    setupIntersectionObserver() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.addGlassEffect(entry.target);
                }
            });
        }, {
            threshold: 0.1
        });

        // Observe all glass elements
        document.querySelectorAll('.glass-panel, .glass-card, .glass-btn').forEach(el => {
            observer.observe(el);
        });
    }

    addGlassEffect(element) {
        if (element.classList.contains('glass-enhanced')) return;
        
        element.classList.add('glass-enhanced');
        
        // Add hover effect
        element.addEventListener('mouseenter', () => {
            this.createRippleEffect(element);
        });
        
        // Add click effect
        element.addEventListener('click', (e) => {
            this.createClickEffect(element, e);
        });
    }

    createRippleEffect(element) {
        const ripple = document.createElement('div');
        ripple.className = 'ripple-effect';
        ripple.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
            border-radius: inherit;
            opacity: 0;
            animation: ripple 0.6s ease-out;
            pointer-events: none;
            z-index: 1;
        `;
        
        element.style.position = 'relative';
        element.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    createClickEffect(element, event) {
        const rect = element.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        const clickEffect = document.createElement('div');
        clickEffect.className = 'click-effect';
        clickEffect.style.cssText = `
            position: absolute;
            left: ${x}px;
            top: ${y}px;
            width: 0;
            height: 0;
            background: radial-gradient(circle, rgba(255,255,255,0.6) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: clickExpand 0.4s ease-out;
            pointer-events: none;
            z-index: 2;
        `;
        
        element.appendChild(clickEffect);
        
        setTimeout(() => {
            clickEffect.remove();
        }, 400);
    }

    startAnimationLoop() {
        const animate = () => {
            this.updateBlobs();
            requestAnimationFrame(animate);
        };
        animate();
    }

    updateBlobs() {
        this.blobs.forEach(blob => {
            // Update position
            blob.x += blob.vx;
            blob.y += blob.vy;
            
            // Bounce off edges
            if (blob.x <= 0 || blob.x >= window.innerWidth) {
                blob.vx *= -0.8;
                blob.x = Math.max(0, Math.min(window.innerWidth, blob.x));
            }
            if (blob.y <= 0 || blob.y >= window.innerHeight) {
                blob.vy *= -0.8;
                blob.y = Math.max(0, Math.min(window.innerHeight, blob.y));
            }
            
            // Apply friction
            blob.vx *= 0.99;
            blob.vy *= 0.99;
            
            // Update DOM element
            blob.element.style.transform = `translate(${blob.x - blob.size/2}px, ${blob.y - blob.size/2}px)`;
        });
    }

    // Particle system for enhanced effects
    createParticleSystem(container) {
        const particles = [];
        const particleCount = 20;
        
        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'glass-particle';
            particle.style.cssText = `
                position: absolute;
                width: 2px;
                height: 2px;
                background: rgba(255, 255, 255, 0.6);
                border-radius: 50%;
                pointer-events: none;
                animation: particleFloat ${2 + Math.random() * 3}s infinite ease-in-out;
                animation-delay: ${Math.random() * 2}s;
            `;
            
            particle.style.left = `${Math.random() * 100}%`;
            particle.style.top = `${Math.random() * 100}%`;
            
            container.appendChild(particle);
            particles.push(particle);
        }
        
        return particles;
    }

    // Dynamic color shifting
    shiftColors() {
        const hue = (Date.now() / 50) % 360;
        document.documentElement.style.setProperty('--dynamic-hue', hue);
    }

    // Responsive liquid effects
    handleResize() {
        this.blobs.forEach(blob => {
            blob.x = Math.min(blob.x, window.innerWidth);
            blob.y = Math.min(blob.y, window.innerHeight);
        });
    }

    // Performance optimization
    optimizeForDevice() {
        const isLowEnd = navigator.hardwareConcurrency <= 4 || 
                        /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        
        if (isLowEnd) {
            // Reduce blob count and effects for low-end devices
            this.blobs = this.blobs.slice(0, 3);
            document.documentElement.style.setProperty('--blur-medium', 'blur(8px)');
            document.documentElement.style.setProperty('--blur-heavy', 'blur(12px)');
        }
    }

    // Cleanup method
    destroy() {
        if (this.liquidOverlay) {
            this.liquidOverlay.remove();
        }
        this.blobs = [];
        this.isInitialized = false;
    }
}

// CSS animations for effects
const liquidEffectsCSS = `
@keyframes ripple {
    0% {
        opacity: 0;
        transform: scale(0);
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0;
        transform: scale(1);
    }
}

@keyframes clickExpand {
    0% {
        width: 0;
        height: 0;
        opacity: 1;
    }
    100% {
        width: 100px;
        height: 100px;
        opacity: 0;
    }
}

@keyframes particleFloat {
    0%, 100% {
        transform: translateY(0) scale(1);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-20px) scale(1.2);
        opacity: 1;
    }
}

.glass-enhanced {
    overflow: hidden;
}

.liquid-blob {
    transition: transform 0.1s ease-out;
}

/* Responsive optimizations */
@media (max-width: 768px) {
    .liquid-blob {
        filter: blur(20px) !important;
    }
}

@media (prefers-reduced-motion: reduce) {
    .liquid-blob,
    .ripple-effect,
    .click-effect,
    .glass-particle {
        animation: none !important;
        transition: none !important;
    }
}
`;

// Inject CSS
const style = document.createElement('style');
style.textContent = liquidEffectsCSS;
document.head.appendChild(style);

// Initialize liquid effects when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.liquidEffects = new LiquidEffects();
    });
} else {
    window.liquidEffects = new LiquidEffects();
}

// Handle window resize
window.addEventListener('resize', () => {
    if (window.liquidEffects) {
        window.liquidEffects.handleResize();
    }
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LiquidEffects;
}
