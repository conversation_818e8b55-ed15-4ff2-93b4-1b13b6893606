/**
 * Docker UI Application - Main application logic
 * Enhanced with macOS-inspired interactions and glass morphism effects
 */

class DockerUI {
    constructor() {
        this.currentView = 'containers';
        this.containers = [];
        this.images = [];
        this.volumes = [];
        this.networks = [];
        this.isLoading = false;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadInitialData();
        this.setupKeyboardShortcuts();
        this.initializeTooltips();
    }

    setupEventListeners() {
        // Navigation tabs
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const view = e.currentTarget.dataset.view;
                this.switchView(view);
            });
        });

        // Action buttons
        document.getElementById('refresh-btn')?.addEventListener('click', () => {
            this.refreshCurrentView();
        });

        document.getElementById('settings-btn')?.addEventListener('click', () => {
            this.openSettings();
        });

        // Modal close
        document.getElementById('modal-close')?.addEventListener('click', () => {
            this.closeModal();
        });

        document.getElementById('modal-overlay')?.addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                this.closeModal();
            }
        });

        // Search functionality
        document.querySelectorAll('.search-box input').forEach(input => {
            input.addEventListener('input', (e) => {
                this.handleSearch(e.target.value);
            });
        });

        // Container actions
        this.setupContainerActions();
    }

    setupContainerActions() {
        document.addEventListener('click', (e) => {
            const containerCard = e.target.closest('.container-card');
            if (!containerCard) return;

            const containerId = containerCard.dataset.containerId;
            
            if (e.target.closest('.icon-btn[title="Start/Stop"]')) {
                this.toggleContainer(containerId);
            } else if (e.target.closest('.icon-btn[title="Restart"]')) {
                this.restartContainer(containerId);
            } else if (e.target.closest('.icon-btn[title="More"]')) {
                this.showContainerMenu(containerId, e.target);
            }
        });
    }

    switchView(viewName) {
        // Update active tab
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-view="${viewName}"]`).classList.add('active');

        // Update view container
        document.querySelectorAll('.view-container').forEach(view => {
            view.classList.remove('active');
        });
        document.getElementById(`${viewName}-view`).classList.add('active');

        this.currentView = viewName;
        this.loadViewData(viewName);
    }

    async loadViewData(viewName) {
        this.setLoading(true);
        
        try {
            switch (viewName) {
                case 'containers':
                    await this.loadContainers();
                    break;
                case 'images':
                    await this.loadImages();
                    break;
                case 'volumes':
                    await this.loadVolumes();
                    break;
                case 'networks':
                    await this.loadNetworks();
                    break;
            }
        } catch (error) {
            this.showError(`Failed to load ${viewName}: ${error.message}`);
        } finally {
            this.setLoading(false);
        }
    }

    async loadContainers() {
        // Simulate API call - replace with actual Docker API calls
        const mockContainers = [
            {
                id: 'nginx-proxy-1',
                name: 'nginx-proxy',
                image: 'nginx:latest',
                status: 'running',
                ports: ['80:80', '443:443'],
                cpu: 12,
                memory: '256MB',
                created: '2 days ago'
            },
            {
                id: 'redis-cache-1',
                name: 'redis-cache',
                image: 'redis:alpine',
                status: 'stopped',
                ports: ['6379:6379'],
                cpu: 0,
                memory: '0MB',
                created: '1 week ago'
            },
            {
                id: 'postgres-db-1',
                name: 'postgres-db',
                image: 'postgres:13',
                status: 'running',
                ports: ['5432:5432'],
                cpu: 8,
                memory: '512MB',
                created: '3 days ago'
            }
        ];

        this.containers = mockContainers;
        this.renderContainers();
    }

    renderContainers() {
        const grid = document.querySelector('.containers-grid');
        if (!grid) return;

        grid.innerHTML = '';

        this.containers.forEach(container => {
            const card = this.createContainerCard(container);
            grid.appendChild(card);
        });

        // Add glass effects to new cards
        setTimeout(() => {
            grid.querySelectorAll('.container-card').forEach(card => {
                if (window.liquidEffects) {
                    window.liquidEffects.addGlassEffect(card);
                }
            });
        }, 100);
    }

    createContainerCard(container) {
        const card = document.createElement('div');
        card.className = 'container-card glass-card';
        card.dataset.containerId = container.id;

        const statusClass = container.status === 'running' ? 'running' : 'stopped';
        const actionIcon = container.status === 'running' ? 'fa-power-off' : 'fa-play';

        card.innerHTML = `
            <div class="card-header">
                <div class="container-status ${statusClass}">
                    <i class="fas fa-circle"></i>
                    <span>${container.status.charAt(0).toUpperCase() + container.status.slice(1)}</span>
                </div>
                <div class="card-actions">
                    <button class="icon-btn" title="Start/Stop">
                        <i class="fas ${actionIcon}"></i>
                    </button>
                    <button class="icon-btn" title="Restart">
                        <i class="fas fa-redo"></i>
                    </button>
                    <button class="icon-btn" title="More">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                </div>
            </div>
            <div class="card-content">
                <h3>${container.name}</h3>
                <p class="container-image">${container.image}</p>
                <div class="container-ports">
                    ${container.ports.map(port => `<span class="port-badge">${port}</span>`).join('')}
                </div>
            </div>
            <div class="card-footer">
                <div class="resource-usage">
                    <div class="usage-item">
                        <i class="fas fa-microchip"></i>
                        <span>${container.cpu}%</span>
                    </div>
                    <div class="usage-item">
                        <i class="fas fa-memory"></i>
                        <span>${container.memory}</span>
                    </div>
                </div>
                <time class="created-time">Created ${container.created}</time>
            </div>
        `;

        return card;
    }

    async toggleContainer(containerId) {
        const container = this.containers.find(c => c.id === containerId);
        if (!container) return;

        this.setLoading(true);
        
        try {
            // Simulate API call
            await this.delay(1000);
            
            container.status = container.status === 'running' ? 'stopped' : 'running';
            container.cpu = container.status === 'running' ? Math.floor(Math.random() * 20) : 0;
            container.memory = container.status === 'running' ? `${Math.floor(Math.random() * 500) + 100}MB` : '0MB';
            
            this.renderContainers();
            this.showSuccess(`Container ${container.name} ${container.status}`);
        } catch (error) {
            this.showError(`Failed to toggle container: ${error.message}`);
        } finally {
            this.setLoading(false);
        }
    }

    async restartContainer(containerId) {
        const container = this.containers.find(c => c.id === containerId);
        if (!container) return;

        this.setLoading(true);
        
        try {
            await this.delay(1500);
            this.showSuccess(`Container ${container.name} restarted`);
        } catch (error) {
            this.showError(`Failed to restart container: ${error.message}`);
        } finally {
            this.setLoading(false);
        }
    }

    showContainerMenu(containerId, button) {
        const container = this.containers.find(c => c.id === containerId);
        if (!container) return;

        const menu = document.createElement('div');
        menu.className = 'context-menu glass-panel';
        menu.innerHTML = `
            <div class="menu-item" data-action="logs">
                <i class="fas fa-file-alt"></i>
                <span>View Logs</span>
            </div>
            <div class="menu-item" data-action="exec">
                <i class="fas fa-terminal"></i>
                <span>Execute Shell</span>
            </div>
            <div class="menu-item" data-action="inspect">
                <i class="fas fa-search"></i>
                <span>Inspect</span>
            </div>
            <div class="menu-separator"></div>
            <div class="menu-item danger" data-action="remove">
                <i class="fas fa-trash"></i>
                <span>Remove</span>
            </div>
        `;

        // Position menu
        const rect = button.getBoundingClientRect();
        menu.style.position = 'fixed';
        menu.style.top = `${rect.bottom + 5}px`;
        menu.style.left = `${rect.left}px`;
        menu.style.zIndex = '1001';

        document.body.appendChild(menu);

        // Handle menu clicks
        menu.addEventListener('click', (e) => {
            const action = e.target.closest('.menu-item')?.dataset.action;
            if (action) {
                this.handleContainerAction(containerId, action);
            }
            menu.remove();
        });

        // Close menu on outside click
        setTimeout(() => {
            document.addEventListener('click', function closeMenu(e) {
                if (!menu.contains(e.target)) {
                    menu.remove();
                    document.removeEventListener('click', closeMenu);
                }
            });
        }, 0);
    }

    handleContainerAction(containerId, action) {
        const container = this.containers.find(c => c.id === containerId);
        if (!container) return;

        switch (action) {
            case 'logs':
                this.showContainerLogs(container);
                break;
            case 'exec':
                this.openTerminal(container);
                break;
            case 'inspect':
                this.inspectContainer(container);
                break;
            case 'remove':
                this.removeContainer(container);
                break;
        }
    }

    showContainerLogs(container) {
        const modalContent = `
            <div class="logs-container">
                <div class="logs-header">
                    <h4>Logs for ${container.name}</h4>
                    <button class="glass-btn" onclick="this.closest('.logs-container').querySelector('.logs-content').innerHTML = 'Refreshing...'">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
                <div class="logs-content">
                    <pre>2024-06-20 12:44:55 [INFO] Container started successfully
2024-06-20 12:44:56 [INFO] Listening on port 80
2024-06-20 12:45:00 [INFO] Health check passed
2024-06-20 12:45:30 [INFO] Processing request from 192.168.1.100</pre>
                </div>
            </div>
        `;
        
        this.openModal(`Container Logs - ${container.name}`, modalContent);
    }

    openModal(title, content) {
        const modal = document.getElementById('modal-overlay');
        const modalTitle = document.getElementById('modal-title');
        const modalContent = document.getElementById('modal-content');
        
        modalTitle.textContent = title;
        modalContent.innerHTML = content;
        modal.classList.add('active');
    }

    closeModal() {
        document.getElementById('modal-overlay').classList.remove('active');
    }

    handleSearch(query) {
        const filteredContainers = this.containers.filter(container =>
            container.name.toLowerCase().includes(query.toLowerCase()) ||
            container.image.toLowerCase().includes(query.toLowerCase())
        );
        
        // Re-render with filtered results
        const grid = document.querySelector('.containers-grid');
        if (grid) {
            grid.innerHTML = '';
            filteredContainers.forEach(container => {
                const card = this.createContainerCard(container);
                grid.appendChild(card);
            });
        }
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'r':
                        e.preventDefault();
                        this.refreshCurrentView();
                        break;
                    case '1':
                        e.preventDefault();
                        this.switchView('containers');
                        break;
                    case '2':
                        e.preventDefault();
                        this.switchView('images');
                        break;
                    case '3':
                        e.preventDefault();
                        this.switchView('volumes');
                        break;
                    case '4':
                        e.preventDefault();
                        this.switchView('networks');
                        break;
                }
            }
            
            if (e.key === 'Escape') {
                this.closeModal();
            }
        });
    }

    initializeTooltips() {
        // Simple tooltip implementation
        document.querySelectorAll('[title]').forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                const tooltip = document.createElement('div');
                tooltip.className = 'tooltip glass-panel';
                tooltip.textContent = e.target.title;
                tooltip.style.cssText = `
                    position: fixed;
                    z-index: 1002;
                    padding: 8px 12px;
                    font-size: 12px;
                    white-space: nowrap;
                    pointer-events: none;
                `;
                
                document.body.appendChild(tooltip);
                
                const rect = e.target.getBoundingClientRect();
                tooltip.style.left = `${rect.left + rect.width / 2 - tooltip.offsetWidth / 2}px`;
                tooltip.style.top = `${rect.top - tooltip.offsetHeight - 5}px`;
                
                e.target.addEventListener('mouseleave', () => {
                    tooltip.remove();
                }, { once: true });
            });
        });
    }

    async loadInitialData() {
        await this.loadContainers();
    }

    async refreshCurrentView() {
        await this.loadViewData(this.currentView);
        this.showSuccess('Data refreshed');
    }

    setLoading(loading) {
        this.isLoading = loading;
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            const icon = refreshBtn.querySelector('i');
            if (loading) {
                icon.classList.add('fa-spin');
            } else {
                icon.classList.remove('fa-spin');
            }
        }
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification glass-panel ${type}`;
        notification.innerHTML = `
            <i class="fas ${type === 'success' ? 'fa-check' : type === 'error' ? 'fa-exclamation-triangle' : 'fa-info'}"></i>
            <span>${message}</span>
        `;
        
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1003;
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 8px;
            animation: slideInRight 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    // Utility methods
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Placeholder methods for other views
    async loadImages() {
        // Implement image loading
    }

    async loadVolumes() {
        // Implement volume loading
    }

    async loadNetworks() {
        // Implement network loading
    }

    openSettings() {
        const settingsContent = `
            <div class="settings-container">
                <h4>Application Settings</h4>
                <div class="setting-item">
                    <label>Theme</label>
                    <select class="glass-input">
                        <option>Dark</option>
                        <option>Light</option>
                        <option>Auto</option>
                    </select>
                </div>
                <div class="setting-item">
                    <label>Auto-refresh interval</label>
                    <select class="glass-input">
                        <option>5 seconds</option>
                        <option>10 seconds</option>
                        <option>30 seconds</option>
                        <option>1 minute</option>
                    </select>
                </div>
            </div>
        `;
        
        this.openModal('Settings', settingsContent);
    }
}

// Additional CSS for notifications and context menus
const additionalCSS = `
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.context-menu {
    min-width: 180px;
    padding: 8px 0;
    border-radius: 8px;
}

.menu-item {
    padding: 8px 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-size: 14px;
}

.menu-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.menu-item.danger {
    color: var(--macos-red);
}

.menu-separator {
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
    margin: 4px 0;
}

.notification.success {
    border-left: 3px solid var(--macos-green);
}

.notification.error {
    border-left: 3px solid var(--macos-red);
}

.logs-container {
    max-height: 400px;
}

.logs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.logs-content {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 16px;
    max-height: 300px;
    overflow-y: auto;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 12px;
    line-height: 1.4;
}

.tooltip {
    font-size: 12px !important;
    padding: 4px 8px !important;
}
`;

// Inject additional CSS
const additionalStyle = document.createElement('style');
additionalStyle.textContent = additionalCSS;
document.head.appendChild(additionalStyle);

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    window.dockerUI = new DockerUI();
});
