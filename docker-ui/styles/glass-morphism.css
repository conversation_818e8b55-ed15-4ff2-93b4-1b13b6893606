/* Glass Morphism Base Styles */
:root {
    /* Glass Morphism Colors */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-bg-light: rgba(255, 255, 255, 0.15);
    --glass-bg-dark: rgba(0, 0, 0, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    --glass-shadow-hover: 0 12px 40px rgba(0, 0, 0, 0.15);
    
    /* Blur Effects */
    --blur-light: blur(10px);
    --blur-medium: blur(16px);
    --blur-heavy: blur(24px);
    
    /* Liquid Glass Colors */
    --liquid-primary: rgba(0, 122, 255, 0.3);
    --liquid-secondary: rgba(88, 86, 214, 0.3);
    --liquid-accent: rgba(255, 45, 85, 0.3);
    
    /* Transitions */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Glass Panel Base */
.glass-panel {
    background: var(--glass-bg);
    backdrop-filter: var(--blur-medium);
    -webkit-backdrop-filter: var(--blur-medium);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    box-shadow: var(--glass-shadow);
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.glass-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(255, 255, 255, 0.4) 50%, 
        transparent 100%);
    z-index: 1;
}

.glass-panel:hover {
    background: var(--glass-bg-light);
    box-shadow: var(--glass-shadow-hover);
    transform: translateY(-2px);
}

/* Glass Card */
.glass-card {
    background: var(--glass-bg);
    backdrop-filter: var(--blur-light);
    -webkit-backdrop-filter: var(--blur-light);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    box-shadow: var(--glass-shadow);
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.glass-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.1) 0%, 
        transparent 50%, 
        rgba(255, 255, 255, 0.05) 100%);
    pointer-events: none;
    z-index: 1;
}

.glass-card:hover {
    background: var(--glass-bg-light);
    box-shadow: var(--glass-shadow-hover);
    transform: translateY(-4px) scale(1.02);
}

/* Glass Button */
.glass-btn {
    background: var(--glass-bg);
    backdrop-filter: var(--blur-light);
    -webkit-backdrop-filter: var(--blur-light);
    border: 1px solid var(--glass-border);
    border-radius: 10px;
    color: rgba(255, 255, 255, 0.9);
    padding: 8px 16px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 8px;
}

.glass-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(255, 255, 255, 0.2) 50%, 
        transparent 100%);
    transition: left 0.5s ease;
    z-index: 1;
}

.glass-btn:hover::before {
    left: 100%;
}

.glass-btn:hover {
    background: var(--glass-bg-light);
    box-shadow: var(--glass-shadow);
    transform: translateY(-2px);
}

.glass-btn:active {
    transform: translateY(0) scale(0.98);
}

.glass-btn.primary {
    background: linear-gradient(135deg, 
        var(--liquid-primary) 0%, 
        var(--liquid-secondary) 100%);
    border: 1px solid rgba(0, 122, 255, 0.3);
}

.glass-btn.primary:hover {
    background: linear-gradient(135deg, 
        rgba(0, 122, 255, 0.4) 0%, 
        rgba(88, 86, 214, 0.4) 100%);
}

/* Glass Input */
.glass-input {
    background: var(--glass-bg);
    backdrop-filter: var(--blur-light);
    -webkit-backdrop-filter: var(--blur-light);
    border: 1px solid var(--glass-border);
    border-radius: 10px;
    padding: 8px 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: var(--transition-smooth);
}

.glass-input:focus-within {
    background: var(--glass-bg-light);
    border-color: rgba(0, 122, 255, 0.5);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.glass-input input {
    background: transparent;
    border: none;
    outline: none;
    color: rgba(255, 255, 255, 0.9);
    flex: 1;
    font-size: 14px;
}

.glass-input input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.glass-input i {
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
}

/* Liquid Glass Effects */
.liquid-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    opacity: 0.6;
}

.liquid-blob {
    position: absolute;
    border-radius: 50%;
    filter: blur(40px);
    animation: liquidFloat 20s infinite ease-in-out;
    mix-blend-mode: multiply;
}

.liquid-blob:nth-child(1) {
    width: 300px;
    height: 300px;
    background: var(--liquid-primary);
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.liquid-blob:nth-child(2) {
    width: 200px;
    height: 200px;
    background: var(--liquid-secondary);
    top: 60%;
    right: 10%;
    animation-delay: -5s;
}

.liquid-blob:nth-child(3) {
    width: 250px;
    height: 250px;
    background: var(--liquid-accent);
    bottom: 10%;
    left: 50%;
    animation-delay: -10s;
}

@keyframes liquidFloat {
    0%, 100% {
        transform: translate(0, 0) scale(1);
    }
    25% {
        transform: translate(30px, -50px) scale(1.1);
    }
    50% {
        transform: translate(-20px, 20px) scale(0.9);
    }
    75% {
        transform: translate(50px, 10px) scale(1.05);
    }
}

/* Shimmer Effect */
.shimmer {
    position: relative;
    overflow: hidden;
}

.shimmer::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(255, 255, 255, 0.4) 50%, 
        transparent 100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Pulse Effect */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 122, 255, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(0, 122, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 122, 255, 0);
    }
}

/* Glow Effect */
.glow {
    position: relative;
}

.glow::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, 
        var(--liquid-primary), 
        var(--liquid-secondary), 
        var(--liquid-accent));
    border-radius: inherit;
    z-index: -1;
    filter: blur(8px);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.glow:hover::before {
    opacity: 0.7;
}

/* Frosted Glass Variant */
.frosted-glass {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Liquid Button */
.liquid-btn {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, 
        var(--liquid-primary) 0%, 
        var(--liquid-secondary) 100%);
    border: none;
    border-radius: 12px;
    color: white;
    padding: 12px 24px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-smooth);
}

.liquid-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease, height 0.6s ease;
}

.liquid-btn:hover::before {
    width: 300px;
    height: 300px;
}

.liquid-btn:active {
    transform: scale(0.95);
}

/* Responsive Glass Effects */
@media (max-width: 768px) {
    .glass-panel, .glass-card {
        backdrop-filter: var(--blur-light);
        -webkit-backdrop-filter: var(--blur-light);
    }
    
    .liquid-blob {
        filter: blur(20px);
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .glass-panel, .glass-card, .glass-btn {
        transition: none;
    }
    
    .liquid-blob {
        animation: none;
    }
    
    .shimmer::after {
        animation: none;
    }
}
