/* macOS-Inspired Theme */
:root {
    /* macOS Color Palette */
    --macos-blue: #007AFF;
    --macos-blue-light: #5AC8FA;
    --macos-blue-dark: #0051D5;
    --macos-green: #34C759;
    --macos-yellow: #FFCC02;
    --macos-orange: #FF9500;
    --macos-red: #FF3B30;
    --macos-purple: #5856D6;
    --macos-pink: #FF2D92;
    --macos-indigo: #5856D6;
    
    /* macOS System Colors */
    --macos-bg-primary: rgba(28, 28, 30, 0.95);
    --macos-bg-secondary: rgba(44, 44, 46, 0.9);
    --macos-bg-tertiary: rgba(58, 58, 60, 0.8);
    --macos-text-primary: rgba(255, 255, 255, 0.95);
    --macos-text-secondary: rgba(255, 255, 255, 0.7);
    --macos-text-tertiary: rgba(255, 255, 255, 0.5);
    
    /* macOS Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-xxl: 48px;
    
    /* macOS Typography */
    --font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 11px;
    --font-size-sm: 13px;
    --font-size-md: 15px;
    --font-size-lg: 17px;
    --font-size-xl: 20px;
    --font-size-xxl: 24px;
    
    /* macOS Shadows */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.25);
    
    /* macOS Border Radius */
    --radius-sm: 6px;
    --radius-md: 10px;
    --radius-lg: 16px;
    --radius-xl: 20px;
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background: linear-gradient(135deg, 
        #1e3c72 0%, 
        #2a5298 25%, 
        #667eea 50%, 
        #764ba2 75%, 
        #f093fb 100%);
    color: var(--macos-text-primary);
    line-height: 1.5;
    overflow-x: hidden;
    min-height: 100vh;
}

/* Background Container */
.background-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.gradient-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, 
        #667eea 0%, 
        #764ba2 25%, 
        #f093fb 50%, 
        #f5576c 75%, 
        #4facfe 100%);
    animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
    0%, 100% {
        background: linear-gradient(135deg, 
            #667eea 0%, 
            #764ba2 25%, 
            #f093fb 50%, 
            #f5576c 75%, 
            #4facfe 100%);
    }
    25% {
        background: linear-gradient(135deg, 
            #4facfe 0%, 
            #00f2fe 25%, 
            #667eea 50%, 
            #764ba2 75%, 
            #f093fb 100%);
    }
    50% {
        background: linear-gradient(135deg, 
            #f093fb 0%, 
            #f5576c 25%, 
            #4facfe 50%, 
            #00f2fe 75%, 
            #667eea 100%);
    }
    75% {
        background: linear-gradient(135deg, 
            #764ba2 0%, 
            #667eea 25%, 
            #4facfe 50%, 
            #00f2fe 75%, 
            #f093fb 100%);
    }
}

/* App Container */
.app-container {
    min-height: 100vh;
    padding: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

/* Top Navigation */
.top-nav {
    padding: var(--spacing-md) var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
}

.nav-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.app-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--macos-text-primary);
}

.app-logo i {
    font-size: var(--font-size-xl);
    color: var(--macos-blue);
}

.nav-tabs {
    display: flex;
    gap: var(--spacing-xs);
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-xs);
    border-radius: var(--radius-md);
}

.nav-tab {
    background: transparent;
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-sm);
    color: var(--macos-text-secondary);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.nav-tab:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--macos-text-primary);
}

.nav-tab.active {
    background: rgba(255, 255, 255, 0.2);
    color: var(--macos-text-primary);
    box-shadow: var(--shadow-sm);
}

.nav-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.action-btn {
    background: transparent;
    border: none;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    color: var(--macos-text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--macos-text-primary);
}

.profile-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

/* Main Content */
.main-content {
    flex: 1;
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: var(--spacing-md);
    min-height: 0;
}

/* Sidebar */
.sidebar {
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    height: fit-content;
}

.sidebar-section h3 {
    font-size: var(--font-size-md);
    font-weight: 600;
    color: var(--macos-text-primary);
    margin-bottom: var(--spacing-md);
}

.sidebar-btn {
    width: 100%;
    justify-content: flex-start;
    text-align: left;
    font-size: var(--font-size-sm);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-xs);
}

.sidebar-btn i {
    width: 16px;
    text-align: center;
}

/* System Stats */
.system-stats {
    padding: var(--spacing-md);
}

.stat-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.stat-item:last-child {
    margin-bottom: 0;
}

.stat-label {
    font-size: var(--font-size-xs);
    color: var(--macos-text-secondary);
    font-weight: 500;
}

.stat-bar {
    flex: 1;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    margin: 0 var(--spacing-sm);
    overflow: hidden;
}

.stat-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--macos-blue), var(--macos-blue-light));
    border-radius: 2px;
    transition: width 0.3s ease;
}

.stat-value {
    font-size: var(--font-size-xs);
    color: var(--macos-text-primary);
    font-weight: 600;
    min-width: 30px;
    text-align: right;
}

/* Activity List */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.activity-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    background: rgba(255, 255, 255, 0.05);
    font-size: var(--font-size-xs);
}

.activity-item i {
    width: 12px;
    text-align: center;
}

.activity-item span {
    flex: 1;
    color: var(--macos-text-secondary);
}

.activity-item time {
    color: var(--macos-text-tertiary);
    font-size: 10px;
}

.text-success { color: var(--macos-green); }
.text-warning { color: var(--macos-yellow); }
.text-info { color: var(--macos-blue); }
.text-danger { color: var(--macos-red); }

/* Content Panel */
.content-panel {
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.view-container {
    display: none;
    flex-direction: column;
    height: 100%;
}

.view-container.active {
    display: flex;
}

.view-header {
    padding: var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.view-header h2 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--macos-text-primary);
}

.view-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.search-box {
    min-width: 250px;
}

/* Container Grid */
.containers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: var(--spacing-md);
    padding: 0 var(--spacing-lg) var(--spacing-lg);
}

/* Container Card */
.container-card {
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.container-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-xs);
    font-weight: 500;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
}

.container-status.running {
    background: rgba(52, 199, 89, 0.2);
    color: var(--macos-green);
}

.container-status.stopped {
    background: rgba(255, 59, 48, 0.2);
    color: var(--macos-red);
}

.container-status i {
    font-size: 8px;
}

.card-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.icon-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    width: 28px;
    height: 28px;
    border-radius: var(--radius-sm);
    color: var(--macos-text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.icon-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--macos-text-primary);
}

.card-content h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--macos-text-primary);
    margin-bottom: var(--spacing-xs);
}

.container-image {
    font-size: var(--font-size-sm);
    color: var(--macos-text-secondary);
    margin-bottom: var(--spacing-sm);
}

.container-ports {
    display: flex;
    gap: var(--spacing-xs);
    flex-wrap: wrap;
}

.port-badge {
    background: rgba(0, 122, 255, 0.2);
    color: var(--macos-blue);
    padding: 2px var(--spacing-xs);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    padding-top: var(--spacing-md);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.resource-usage {
    display: flex;
    gap: var(--spacing-md);
}

.usage-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--macos-text-secondary);
}

.created-time {
    font-size: var(--font-size-xs);
    color: var(--macos-text-tertiary);
}

/* Content Placeholder */
.content-placeholder {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    color: var(--macos-text-secondary);
}

.content-placeholder i {
    font-size: 48px;
    opacity: 0.5;
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-overlay.active {
    display: flex;
}

.modal {
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--macos-text-primary);
}

.modal-close {
    background: none;
    border: none;
    color: var(--macos-text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--macos-text-primary);
}

.modal-content {
    padding: var(--spacing-lg);
    max-height: 60vh;
    overflow-y: auto;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content {
        grid-template-columns: 1fr;
    }
    
    .sidebar {
        order: 2;
    }
    
    .content-panel {
        order: 1;
    }
}

@media (max-width: 768px) {
    .app-container {
        padding: var(--spacing-sm);
    }
    
    .nav-left {
        gap: var(--spacing-md);
    }
    
    .nav-tabs {
        display: none;
    }
    
    .containers-grid {
        grid-template-columns: 1fr;
        padding: 0 var(--spacing-md) var(--spacing-md);
    }
    
    .view-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }
    
    .view-actions {
        justify-content: space-between;
    }
}
